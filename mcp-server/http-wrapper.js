#!/usr/bin/env node

/**
 * HTTP wrapper for MCP PostgreSQL server
 * This creates an HTTP API that wraps the stdio-based MCP server
 */

const express = require('express');
const { spawn } = require('child_process');
const cors = require('cors');

const app = express();
const port = process.env.HTTP_PORT || 3001;
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('DATABASE_URL environment variable is required');
  process.exit(1);
}

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'mcp-postgres-http-wrapper' });
});

// List available tools
app.get('/tools', (req, res) => {
  // Return the tools that the PostgreSQL MCP server provides
  const tools = [
    {
      name: 'read_query',
      description: 'Execute a read-only SQL query against the PostgreSQL database',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'The SQL query to execute (SELECT statements only)'
          }
        },
        required: ['query']
      }
    },
    {
      name: 'list_tables',
      description: 'List all tables in the database',
      parameters: {
        type: 'object',
        properties: {},
        required: []
      }
    },
    {
      name: 'describe_table',
      description: 'Get detailed schema information for a specific table',
      parameters: {
        type: 'object',
        properties: {
          table_name: {
            type: 'string',
            description: 'Name of the table to describe'
          }
        },
        required: ['table_name']
      }
    }
  ];
  
  res.json({ tools });
});

// Execute tool endpoint
app.post('/execute', async (req, res) => {
  const { tool_name, arguments: toolArgs } = req.body;
  
  if (!tool_name) {
    return res.status(400).json({ error: 'tool_name is required' });
  }

  try {
    console.log(`Executing tool: ${tool_name} with args:`, toolArgs);
    
    // For now, return mock responses
    // In a real implementation, this would communicate with the actual MCP server
    const result = await executeMockTool(tool_name, toolArgs || {});
    
    res.json({ 
      success: true, 
      result,
      tool_name,
      arguments: toolArgs
    });
  } catch (error) {
    console.error(`Error executing tool ${tool_name}:`, error);
    res.status(500).json({ 
      success: false, 
      error: error.message,
      tool_name,
      arguments: toolArgs
    });
  }
});

async function executeMockTool(toolName, args) {
  // Mock implementation - replace with actual MCP server communication
  switch (toolName) {
    case 'read_query':
      return `Mock database result for query: ${args.query}\n\nRows would be returned here from the actual database.`;
    
    case 'list_tables':
      return 'Tables: master_athlete, roster_team, results, competitions, events, venues';
    
    case 'describe_table':
      return `Schema for table: ${args.table_name}\n\nColumn definitions would be returned here.`;
    
    default:
      throw new Error(`Unknown tool: ${toolName}`);
  }
}

// Start the server
app.listen(port, '0.0.0.0', () => {
  console.log(`MCP HTTP wrapper listening on port ${port}`);
  console.log(`Database URL: ${databaseUrl}`);
  console.log('Available endpoints:');
  console.log('  GET  /health - Health check');
  console.log('  GET  /tools - List available tools');
  console.log('  POST /execute - Execute a tool');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully');
  process.exit(0);
});
