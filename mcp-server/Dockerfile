# Dockerfile for standalone MCP PostgreSQL server with HTTP wrapper
FROM node:18-alpine

# Install required packages
RUN npm install -g @modelcontextprotocol/server-postgres express cors

# Create app directory
WORKDIR /app

# Copy HTTP wrapper and startup script
COPY http-wrapper.js /app/
COPY start-mcp-server.sh /usr/local/bin/start-mcp-server.sh
RUN chmod +x /usr/local/bin/start-mcp-server.sh

# Expose port for HTTP API
EXPOSE 3001

# Start the HTTP wrapper
CMD ["node", "/app/http-wrapper.js"]
